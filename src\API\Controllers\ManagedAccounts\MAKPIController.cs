using API.Filters.CustomAuthorization;
using System.Net;
using System.Threading.Tasks;
using API.Helpers;
using Contract.KPI;
using Contract.Utility;
using Microsoft.AspNetCore.Mvc;
using Utility.Resource;
using Contract.Account;
using ManagedAccounts.Interfaces;
using System;
using System.Linq;
using MediatR;
using ManagedAccounts.Models.Queries;
using Microsoft.AspNetCore.Http;
using System.IO;
using OfficeOpenXml;
using ManagedAccounts.Helpers;

namespace API.Controllers.ManagedAccounts
{
    public class MAKPIController : BaseController
    {
        private readonly IKpiService _kpiService;
        private readonly IManagedAccountDetailsService _managedAccountDetailsService;
        private readonly IMediator _mediator;
        public MAKPIController(IKpiService kPIService, IManagedAccountDetailsService mAService, IMediator mediator, IInjectedParameters InjectedParameters, IHelperService helperService) : base(InjectedParameters, helperService)
        {
            _kpiService = kPIService;
            _managedAccountDetailsService = mAService;
            _mediator = mediator;
        }

        [Route("managed-accounts/kpi/mapping/{Id}/{Type}/{moduleID}")]
        [UserFeatureAuthorize((int)Features.KPIsMapping)]
        [HttpGet]
        public async Task<IActionResult> GetKPIMapping(Guid Id, string Type, int moduleID)
        {
            if (Id.Equals(Guid.Empty) || string.IsNullOrWhiteSpace(Type)) return BadRequest();
            
            var mAdetails = await _managedAccountDetailsService.GetByIdAsync(Id);
            
            if(mAdetails == null)
                return BadRequest();

            var result = await _kpiService.GetKPIMapping(mAdetails.UAMId, Type, moduleID);
            return result == null ? JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound) : JsonResponse.Create(HttpStatusCode.OK, result);
        }
        [HttpGet("managed-accounts/kpi/unmapped/{Id}/{Type}/{moduleID}")]
        [UserFeatureAuthorize((int)Features.KPIsMapping)]
        public async Task<IActionResult> GetUnMappedKpi(Guid Id, string Type, int moduleID)
        {
            if (Id.Equals(Guid.Empty) || string.IsNullOrWhiteSpace(Type)) return BadRequest();

            var mAdetails = await _managedAccountDetailsService.GetByIdAsync(Id);

            if (mAdetails == null)
                return BadRequest();
            var result = await _kpiService.GetUnMappedKpi(mAdetails.UAMId, Type, moduleID);
            return Ok(result);
        }

        [HttpPost("managed-accounts/kpi/mapping/{Id}/{type}/{moduleID}")]
        [UserFeatureAuthorize((int)Features.KPIsMapping)]
        public async Task<IActionResult> UpdateKPIMapping(Guid Id, string Type, KpiMappingQueryModel KpiMappingQueryModel, int moduleID)
        {
            if (Id.Equals(Guid.Empty) || string.IsNullOrWhiteSpace(Type)) return BadRequest();

            var mAdetails = await _managedAccountDetailsService.GetByIdAsync(Id);

            if (mAdetails == null)
                return BadRequest();

            await _kpiService.UpdateKPIMapping(mAdetails.UAMId, Type, KpiMappingQueryModel?.KPIMappings, GetCurrentUserId(), moduleID);
            if (KpiMappingQueryModel?.CompanyIds?.Any() == true)
            {
                await _kpiService.CopyKPIToCompanies(new CopyToKpiQueryModel()
                {
                    CompanyId = mAdetails.UAMId,
                    KpiType = Type == "Financial KPIs" ? "Trading Records" : Type,
                    CompanyIds = KpiMappingQueryModel.CompanyIds,
                    UserId = GetCurrentUserId(),
                    ModuleId = moduleID
                });
            }

            return JsonResponse.Create(HttpStatusCode.OK, "Done");
        }

        /// <summary>
        /// Downloads an Excel template for Managed Account Cap Table data import
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="moduleName">The module ID for KPI filtering</param>
        /// <returns>An Excel file containing the managed account cap table template</returns>
        [HttpPost("managed-accounts/import/template")]
        [UserFeatureAuthorize((int)Features.ManagedAccounts)]
        public async Task<IActionResult> DownloadManagedCapTableTemplate(DownloadManagedCapTableTemplateQuery query)
        {
            if (query.ManagedAccountId == Guid.Empty || string.IsNullOrEmpty(query.ModuleName))
            {
                return BadRequest("Invalid managed account ID or module ID");
            }

            query.UserId = GetCurrentUserId();

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                return new ExcelContentResult(result.FileName, result.FilePath);
            }

            return BadRequest(result.ErrorMessage);
        }

        /// <summary>
        /// Uploads and processes an Excel file for managed account data import
        /// </summary>
        /// <param name="file">The Excel file to upload</param>
        /// <param name="moduleName">The module name for processing</param>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <returns>Processing status and results</returns>
        [HttpPost("managed-accounts/import/excel")]
        [UserFeatureAuthorize((int)Features.ManagedAccounts)]
        public async Task<IActionResult> UploadManagedAccountExcel([FromForm] IFormFile file, [FromForm] string moduleName, [FromForm] Guid managedAccountId)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded or file is empty");
            }

            if (managedAccountId == Guid.Empty || string.IsNullOrWhiteSpace(moduleName))
            {
                return BadRequest("Invalid managed account ID or module name");
            }

            // Validate file type
            var extension = Path.GetExtension(file.FileName).ToLower();
            var validFileTypes = new[] { ".xlsx", ".xls" };
            if (!validFileTypes.Contains(extension))
            {
                return BadRequest("Invalid file format. Only Excel files (.xlsx, .xls) are allowed");
            }

            try
            {
                // Verify managed account exists
                var managedAccountDetails = await _managedAccountDetailsService.GetByIdAsync(managedAccountId);
                if (managedAccountDetails == null)
                {
                    return BadRequest("Managed account not found");
                }

                // Create temporary directory for file processing
                string folderName = Guid.NewGuid().ToString();
                string webRootPath = Path.Combine(Directory.GetCurrentDirectory(), "TempUploads", folderName);
                BulUploadHelper.CreateDirIfNotExists(webRootPath);

                try
                {
                    // Save file to temporary location
                    string fileName = $"{Guid.NewGuid()}{extension}";
                    string fullPath = Path.Combine(webRootPath, fileName);

                    using (var stream = new FileStream(fullPath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    // Process Excel file
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    using var package = new ExcelPackage(new FileInfo(fullPath));

                    var result = await MAExcelProcessorHelper.ProcessManagedAccountExcel(
                        package,
                        moduleName,
                        managedAccountId,
                        managedAccountDetails.UAMId,
                        GetCurrentUserId());

                    return JsonResponse.Create(HttpStatusCode.OK, result);
                }
                finally
                {
                    // Clean up temporary files
                    if (Directory.Exists(webRootPath))
                    {
                        Directory.Delete(webRootPath, true);
                    }
                }
            }
            catch (Exception ex)
            {
                return JsonResponse.Create(HttpStatusCode.InternalServerError, new { Error = ex.Message });
            }
        }

    }
}
