using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OfficeOpenXml;
using Contract.Utility;

namespace ManagedAccounts.Helpers
{
    /// <summary>
    /// Helper class for processing Excel files for Managed Account data import
    /// </summary>
    public static class MAExcelProcessorHelper
    {
        /// <summary>
        /// Processes an Excel file for managed account data import
        /// Note: Only one sheet will be processed as input
        /// </summary>
        /// <param name="package">The Excel package to process</param>
        /// <param name="moduleName">The module name for processing</param>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="uamId">The UAM ID from managed account details</param>
        /// <param name="userId">The current user ID</param>
        /// <returns>Processing status and results</returns>
        public static async Task<List<Status>> ProcessManagedAccountExcel(
            ExcelPackage package, 
            string moduleName, 
            Guid managedAccountId, 
            int uamId, 
            int userId)
        {
            var statusList = new List<Status>();
            
            try
            {
                // Validate package has worksheets
                if (package.Workbook.Worksheets.Count == 0)
                {
                    statusList.Add(new Status
                    {
                        Code = "ERROR",
                        Message = "No worksheets found in the Excel file"
                    });
                    return statusList;
                }

                // Get the first worksheet (as per requirement: only one sheet will be received)
                var worksheet = package.Workbook.Worksheets.First();
                
                // Validate worksheet has data
                if (!worksheet.Cells.Any(c => c.Value != null))
                {
                    statusList.Add(new Status
                    {
                        Code = "ERROR",
                        Message = "The worksheet is empty"
                    });
                    return statusList;
                }

                // Process the worksheet based on module name
                var result = await ProcessWorksheetByModule(worksheet, moduleName, managedAccountId, uamId, userId);
                statusList.AddRange(result);

                // Add success status if no errors
                if (!statusList.Any(s => s.Code == "ERROR"))
                {
                    statusList.Add(new Status
                    {
                        Code = "SUCCESS",
                        Message = $"Excel file processed successfully for module: {moduleName}"
                    });
                }
            }
            catch (Exception ex)
            {
                statusList.Add(new Status
                {
                    Code = "ERROR",
                    Message = $"Error processing Excel file: {ex.Message}"
                });
            }

            return statusList;
        }

        /// <summary>
        /// Processes the worksheet based on the specified module
        /// </summary>
        /// <param name="worksheet">The Excel worksheet to process</param>
        /// <param name="moduleName">The module name for processing</param>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="uamId">The UAM ID from managed account details</param>
        /// <param name="userId">The current user ID</param>
        /// <returns>Processing status results</returns>
        private static async Task<List<Status>> ProcessWorksheetByModule(
            ExcelWorksheet worksheet, 
            string moduleName, 
            Guid managedAccountId, 
            int uamId, 
            int userId)
        {
            var statusList = new List<Status>();

            try
            {
                // Get worksheet dimensions
                var startRow = worksheet.Dimension?.Start.Row ?? 1;
                var endRow = worksheet.Dimension?.End.Row ?? 1;
                var startCol = worksheet.Dimension?.Start.Column ?? 1;
                var endCol = worksheet.Dimension?.End.Column ?? 1;

                // Validate headers (assuming first row contains headers)
                var headers = ExtractHeaders(worksheet, startRow, startCol, endCol);
                var headerValidation = ValidateHeaders(headers, moduleName);
                
                if (!headerValidation.IsValid)
                {
                    statusList.Add(new Status
                    {
                        Code = "ERROR",
                        Message = headerValidation.ErrorMessage
                    });
                    return statusList;
                }

                // Process data rows
                var dataRows = ExtractDataRows(worksheet, startRow + 1, endRow, startCol, endCol);
                var processedData = await ProcessDataByModule(dataRows, headers, moduleName, managedAccountId, uamId, userId);
                
                statusList.AddRange(processedData);
            }
            catch (Exception ex)
            {
                statusList.Add(new Status
                {
                    Code = "ERROR",
                    Message = $"Error processing worksheet for module {moduleName}: {ex.Message}"
                });
            }

            return statusList;
        }

        /// <summary>
        /// Extracts headers from the worksheet
        /// </summary>
        /// <param name="worksheet">The Excel worksheet</param>
        /// <param name="headerRow">The header row number</param>
        /// <param name="startCol">Start column</param>
        /// <param name="endCol">End column</param>
        /// <returns>List of header names</returns>
        private static List<string> ExtractHeaders(ExcelWorksheet worksheet, int headerRow, int startCol, int endCol)
        {
            var headers = new List<string>();
            
            for (int col = startCol; col <= endCol; col++)
            {
                var headerValue = worksheet.Cells[headerRow, col].Value?.ToString()?.Trim() ?? string.Empty;
                headers.Add(headerValue);
            }
            
            return headers;
        }

        /// <summary>
        /// Validates headers based on module requirements
        /// </summary>
        /// <param name="headers">The extracted headers</param>
        /// <param name="moduleName">The module name</param>
        /// <returns>Validation result</returns>
        private static (bool IsValid, string ErrorMessage) ValidateHeaders(List<string> headers, string moduleName)
        {
            // Basic validation - ensure headers are not empty
            if (!headers.Any() || headers.All(string.IsNullOrWhiteSpace))
            {
                return (false, "No valid headers found in the Excel file");
            }

            // Module-specific header validation can be added here
            // For now, we'll do basic validation
            var emptyHeaders = headers.Where(string.IsNullOrWhiteSpace).Count();
            if (emptyHeaders > 0)
            {
                return (false, $"Found {emptyHeaders} empty header(s). All columns must have headers.");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// Extracts data rows from the worksheet
        /// </summary>
        /// <param name="worksheet">The Excel worksheet</param>
        /// <param name="startRow">Start row for data</param>
        /// <param name="endRow">End row for data</param>
        /// <param name="startCol">Start column</param>
        /// <param name="endCol">End column</param>
        /// <returns>List of data rows</returns>
        private static List<List<object>> ExtractDataRows(ExcelWorksheet worksheet, int startRow, int endRow, int startCol, int endCol)
        {
            var dataRows = new List<List<object>>();
            
            for (int row = startRow; row <= endRow; row++)
            {
                var rowData = new List<object>();
                bool hasData = false;
                
                for (int col = startCol; col <= endCol; col++)
                {
                    var cellValue = worksheet.Cells[row, col].Value;
                    rowData.Add(cellValue);
                    
                    if (cellValue != null && !string.IsNullOrWhiteSpace(cellValue.ToString()))
                    {
                        hasData = true;
                    }
                }
                
                // Only add rows that have at least some data
                if (hasData)
                {
                    dataRows.Add(rowData);
                }
            }
            
            return dataRows;
        }

        /// <summary>
        /// Processes data based on module requirements
        /// </summary>
        /// <param name="dataRows">The extracted data rows</param>
        /// <param name="headers">The column headers</param>
        /// <param name="moduleName">The module name</param>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="uamId">The UAM ID</param>
        /// <param name="userId">The current user ID</param>
        /// <returns>Processing status results</returns>
        private static async Task<List<Status>> ProcessDataByModule(
            List<List<object>> dataRows, 
            List<string> headers, 
            string moduleName, 
            Guid managedAccountId, 
            int uamId, 
            int userId)
        {
            var statusList = new List<Status>();
            
            try
            {
                // Log processing information
                statusList.Add(new Status
                {
                    Code = "INFO",
                    Message = $"Processing {dataRows.Count} data rows for module: {moduleName}"
                });

                // Module-specific processing logic can be implemented here
                // For now, we'll add a placeholder for the processing logic
                
                await Task.Delay(100); // Placeholder for async processing
                
                statusList.Add(new Status
                {
                    Code = "INFO",
                    Message = $"Data processing completed for managed account: {managedAccountId}"
                });
            }
            catch (Exception ex)
            {
                statusList.Add(new Status
                {
                    Code = "ERROR",
                    Message = $"Error processing data for module {moduleName}: {ex.Message}"
                });
            }
            
            return statusList;
        }
    }
}
