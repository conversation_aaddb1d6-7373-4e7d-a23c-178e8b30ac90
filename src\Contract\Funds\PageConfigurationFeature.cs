﻿using Shared;
using System.ComponentModel;
namespace Contract.Funds
{
    public enum PageConfigurationFeature
    {
        [Description(Constants.PortfolioCompany)]
        PortfolioCompany = 1,
        Funds = 2,
        Deals = 3,
        Firms = 4,
        Pipeline = 5,
        Dashboard = 6,
        Investor = 7,
        FundCashflow= 8,
        ESG=9
    }
    public enum PageConfigurationSubFeature
    {
        [Description(Constants.StaticInformation)]
        StaticInformation = 1,
        [Description(Constants.KeyPerformanceIndicator)]
        KeyPerformanceIndicator = 2,
        [Description(Constants.CompanyFinancials)]
        CompanyFinancials = 3,
        [Description(Constants.Commentary)]
        Commentary = 4,
        [Description(Constants.BasicDetails)]
        BasicDetails = 5,
        [Description(Constants.PortfolioCompanyFundHoldingDetails)]
        PortfolioCompanyFundHoldingDetails = 6,
        [Description(Constants.FundStaticInformation)]
        FundStaticInformation = 7,
        [Description(Constants.FundTerms)]
        FundTerms = 8,
        [Description(Constants.TrackRecord)]
        TrackRecord = 9,
        [Description(Constants.GeographicLocations)]
        GeographicLocations = 10,
        [Description(Constants.InvestmentProfessionals)]
        InvestmentProfessionals = 11,
        [Description(Constants.GeographicLocations)]
        GeographicLocationsFund = 12,
        [Description(Constants.InvestorInformation)]
        InvestorInformation = 13,
        [Description(Constants.InvestorGeographicalLocations)]
        InvestorGeographicalLocations = 14,
        [Description(Constants.InvestorFunds)]
        InvestorFunds = 15,
        [Description(Constants.InvestorDashboard)]
        InvestorDashboard = 16,
        [Description(Constants.CompanyPerformance)]
        CompanyPerformance = 17,
        [Description(Constants.ValuationData)]
        ValuationData = 18,
        [Description(Constants.ValuationData)]
        MainDashboard = 19,
        [Description(Constants.CashFlowPort)]
        CashFlowPort = 20,
        [Description(Constants.CashFlowTransactionType)]
        CashFlowTransactionType = 21,
        CapTable = 38,
        DataAnalytics = 39,
        Reports = 40,
        [Description(Constants.KeyPerformanceIndicator)]
        OtherKPIs = 41,
        [Description(Constants.SustainableDevelopmentGoalsImages)]
        SustainableDevelopmentGoalsImages = 42,
        [Description(Constants.FundIngestion)]
        FundIngestion = 45,
        FundKpis = 46,
        FundKeyKpis = 49,
        OtherCapTable = 47,
        InvestmentPage = 54,
        ManagedAccountTrackRecord =55,
        PortfolioStatistics =56,
        InvestmentPortfolio =57,
        InvestorCashFlowActivity =58,
    }

    public enum PageSubFieldsDatatTypes
    {
        [Description(Constants.DataTypeDefault)]
        Default = 0,
        [Description(Constants.DataTypeFreeText)]
        FreeText = 1,
        [Description(Constants.DataTypeNumber)]
        Number = 2,
        [Description(Constants.DataTypeCurrency)]
        Currency = 3,
        [Description(Constants.DataTypePercentage)]
        Percentage = 4,
        [Description(Constants.DataTypeMultiple)]
        Multiple = 5,
        [Description(Constants.DataTypeDate)]
        Date = 6,
        [Description(Constants.DataTypeList)]
        List = 7
    }
}
