GO
IF NOT EXISTS(SELECT * FROM [dbo].[M_KpiModules] WHERE [Name] = 'CustomTable5')
BEGIN
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable5',GETDATE(),3,0,'Custom Table5','Custom Table5',1,0,31,0,'CustomTable5'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable6',GETDATE(),3,0,'Custom Table6','Custom Table6',1,0,32,0,'CustomTable6'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable7',GETDATE(),3,0,'Custom Table7','Custom Table7',1,0,33,0,'CustomTable7'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable8',GETDATE(),3,0,'Custom Table8','Custom Table8',1,0,34,0,'CustomTable8'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable9',GETDATE(),3,0,'Custom Table9','Custom Table9',1,0,35,0,'CustomTable9'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI11',GETDATE(),3,0,'Other KPI11','Other KPI11',1,0,36,0,'OtherKPI11'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI12',GETDATE(),3,0,'Other KPI12','Other KPI12',1,0,37,0,'OtherKPI12'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI13',GETDATE(),3,0,'Other KPI13','Other KPI13',1,0,38,0,'OtherKPI13'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI14',GETDATE(),3,0,'Other KPI14','Other KPI14',1,0,39,0,'OtherKPI14'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI15',GETDATE(),3,0,'Other KPI15','Other KPI15',1,0,40,0,'OtherKPI15'		   
)
END
GO
IF NOT EXISTS(SELECT * FROM [dbo].[M_SubPageFields] WHERE [Name] = 'CustomTable5')
BEGIN
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable5','Custom Table5',2,1,0,GETDATE(),3,11,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable6','Custom Table6',2,1,0,GETDATE(),3,12,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable7','Custom Table7',2,1,0,GETDATE(),3,13,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable8','Custom Table8',2,1,0,GETDATE(),3,14,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable9','Custom Table9',2,1,0,GETDATE(),3,15,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI11','Other KPI11',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,11,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI12','Other KPI12',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,12,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI13','Other KPI13',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,13,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI14','Other KPI14',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,14,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI15','Other KPI15',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,15,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Custom Table5')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (125, N'Custom Table5', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable5')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (126, N'Custom Table6', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable6')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (127, N'Custom Table7', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable7')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (128, N'Custom Table8', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable8')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (129, N'Custom Table9', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable9')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (130, N'Other KPI11', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI11')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (131, N'Other KPI12', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI12')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (132, N'Other KPI13', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI13')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (133, N'Other KPI14', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI14')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (134, N'Other KPI15', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI15')
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END

GO
IF EXISTS (SELECT 1 FROM [dbo].[DashboardTrackerConfig] WHERE MapTo IS NOT NULL AND (MaptoType IS NULL OR MaptoType = 0))
BEGIN
	UPDATE [dbo].[DashboardTrackerConfig]
	SET MaptoType = 1
	WHERE MapTo IS NOT NULL AND (MaptoType IS NULL OR MaptoType = 0);
END
GO
IF NOT EXISTS ( SELECT 1 FROM FinancialValueTypes WHERE Name = 'Since Inception' AND IsDeleted = 0)
BEGIN
    INSERT INTO FinancialValueTypes (Name, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, Alias)
    VALUES ('Since Inception', GETDATE(), 3, NULL, NULL, 0, NULL)
END
GO

----------------------------------------- BFB-11438-managed-accounts -------------------------

IF NOT EXISTS(Select * from M_Features Where Feature='Managed Accounts' )
BEGIN
INSERT [dbo].[M_Features] ([FeatureID], [Feature], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureID], [SequenceNo], [AliasName], [FeaturePath])
VALUES (64, N'Managed Accounts', NULL, NULL, 1, 0, GETDATE(), 3, NULL, NULL, NULL, 39, N'Managed Accounts', N'/managed-accounts')
END
GO

IF (NOT EXISTS (SELECT * 
                 FROM [dbo].[Mapping_FeatureAction]
                 WHERE [FeatureID] = 64				 
                ))
BEGIN
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (64, 1, 1, GETDATE(), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (64, 2, 1, GETDATE(), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (64, 3, 1, GETDATE(), 1, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (
	SELECT 1 FROM DashboardTrackerConfig
	WHERE [Name]='SerialNo' AND [FieldType]=1 AND [DataType]=1
)
BEGIN
INSERT INTO [dbo].[DashboardTrackerConfig] (
   [FieldType], [DataType], [Name], [FrequencyType], [StartPeriod], [EndPeriod], [IsPrefix], [TimeSeriesDateFormat], [MapTo], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [MaptoType], [DeletedColumns]
)
VALUES (
    1, 1, 'SerialNo', 1, NULL, NULL, NULL, NULL, NULL, 1, 0, GETDATE(), 1135, NULL, NULL, 0, NULL
)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[MCurrencyFrequency] WHERE [FrequencyCode] = 'DAILY')
BEGIN
    INSERT INTO [dbo].[MCurrencyFrequency] 
        ([FrequencyName], [FrequencyCode], [Description], [CreatedBy])
    VALUES
        ('Daily', 'DAILY', 'Data loaded every day', 1)
END

-- Check if 'Monthly' frequency exists, if not insert it
IF NOT EXISTS (SELECT 1 FROM [dbo].[MCurrencyFrequency] WHERE [FrequencyCode] = 'MONTHLY')
BEGIN
    INSERT INTO [dbo].[MCurrencyFrequency] 
        ([FrequencyName], [FrequencyCode], [Description], [CreatedBy])
    VALUES
        ('Monthly', 'MONTHLY', 'Data loaded once per month', 1)
END

-- Check if 'Quarterly' frequency exists, if not insert it
IF NOT EXISTS (SELECT 1 FROM [dbo].[MCurrencyFrequency] WHERE [FrequencyCode] = 'QUARTERLY')
BEGIN
    INSERT INTO [dbo].[MCurrencyFrequency] 
        ([FrequencyName], [FrequencyCode], [Description], [CreatedBy])
    VALUES
        ('Quarterly', 'QUARTERLY', 'Data loaded once per quarter', 1)
END

-- Check if 'Annual' frequency exists, if not insert it
IF NOT EXISTS (SELECT 1 FROM [dbo].[MCurrencyFrequency] WHERE [FrequencyCode] = 'ANNUAL')
BEGIN
    INSERT INTO [dbo].[MCurrencyFrequency] 
        ([FrequencyName], [FrequencyCode], [Description], [CreatedBy])
    VALUES
        ('Annual', 'ANNUAL', 'Data loaded once per year', 1)
END

-- Check if 'LTM' frequency exists, if not insert it
IF NOT EXISTS (SELECT 1 FROM [dbo].[MCurrencyFrequency] WHERE [FrequencyCode] = 'LTM')
BEGIN
    INSERT INTO [dbo].[MCurrencyFrequency] 
        ([FrequencyName], [FrequencyCode], [Description], [CreatedBy])
    VALUES
        ('LTM', 'LTM', 'Data loaded as needed', 1)
END

-- Check if 'YTD' frequency exists, if not insert it
IF NOT EXISTS (SELECT 1 FROM [dbo].[MCurrencyFrequency] WHERE [FrequencyCode] = 'YTD')
BEGIN
    INSERT INTO [dbo].[MCurrencyFrequency] 
        ([FrequencyName], [FrequencyCode], [Description], [CreatedBy])
    VALUES
        ('YTD', 'YTD', 'Data loaded as needed', 1)
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Managed Account Facts')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (135, N'Managed Account Facts', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Managed Account Facts')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Investment Summary')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (136, N'Investment Summary', 64, NULL,1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Investment Summary')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Performance')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (137, N'Performance', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Performance')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='NAV Data')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (138, N'NAV Data', 64, NULL,1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'NAV Data')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Income Distributions (Investment Page)')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (139, N'Income Distributions (Investment Page)', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Income Distributions (Investment Page)')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Performance Attribution (Current Year)')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (140, N'Performance Attribution (Current Year)', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Performance Attribution (Current Year)')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Track Record')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (141, N'Track Record', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Track Record')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Portfolio Statistics')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (142, N'Portfolio Statistics', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Portfolio Statistics')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Investment Limits')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (143, N'Investment Limits', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Investment Limits')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Top 5 Sectors')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (144, N'Top 5 Sectors', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Top 5 Sectors')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Top 5 Issuers')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (145, N'Top 5 Issuers', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Top 5 Issuers')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Asset Ratings')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (146, N'Asset Ratings', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Asset Ratings')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Asset Classes')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (147, N'Asset Classes', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Asset Classes')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Currencies')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (148, N'Currencies', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Currencies')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Geographies')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (149, N'Geographies', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Geographies')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Sectors')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (150, N'Sectors', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Sectors')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Investment Portfolio')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (151, N'Investment Portfolio', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Investment Portfolio')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Capital Activity')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (152, N'Capital Activity', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Capital Activity')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Income Distributions (Investor Cashflow)')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (153, N'Income Distributions (Investor Cashflow)', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Income Distributions (Investor Cashflow)')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Market Commentary')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (154, N'Market Commentary', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Market Commentary')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Managed Account Commentary')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (155, N'Managed Account Commentary', 64, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Managed Account Commentary')
END
GO



--subfeaturelevel--Managed Account Facts view export import edit

IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Facts') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Facts'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Facts') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Facts'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel-Investment Summary view export import edit

IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Summary') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Summary'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Summary') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Summary'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Performance view export import edit

IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--NAV Data view export import edit

IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'NAV Data'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Income Distributions (Investment Page) view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investment Page)'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Performance Attribution (Current Year) view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Performance Attribution (Current Year)'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Track Record view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Track Record'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Portfolio Statistics view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Portfolio Statistics'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Investment Limits view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Limits'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Top 5 Sectors view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Sectors'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Top 5 Issuers view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Top 5 Issuers'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Asset Ratings view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Ratings'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Asset Classes view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Asset Classes'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Currencies view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Currencies'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Geographies view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Geographies'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Sectors view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Sectors'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Sectors view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Investment Portfolio'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Capital Activity view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Capital Activity'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Income Distributions (Investment Page) view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Income Distributions (Investor Cashflow)'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Market Commentary view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Market Commentary') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Market Commentary'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Market Commentary') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Market Commentary'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Managed Account Commentary view export import edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Commentary') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Commentary'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Commentary') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
    VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Managed Account Commentary'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO

IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Performance'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable1',  PageConfigName = 'ManagedCapTable1' WHERE SubFeature = 'Performance'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'NAV Data'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable2',  PageConfigName = 'ManagedCapTable2' WHERE SubFeature = 'NAV Data'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Income Distributions (Investment Page)'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable3',  PageConfigName = 'ManagedCapTable3' WHERE SubFeature = 'Income Distributions (Investment Page)'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Performance Attribution (Current Year)'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable4',  PageConfigName = 'ManagedCapTable4' WHERE SubFeature = 'Performance Attribution (Current Year)'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Track Record'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable5',  PageConfigName = 'ManagedCapTable5' WHERE SubFeature = 'Track Record'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Portfolio Statistics'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable6',  PageConfigName = 'ManagedCapTable6' WHERE SubFeature = 'Portfolio Statistics'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Investment Limits'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable7',  PageConfigName = 'ManagedCapTable7' WHERE SubFeature = 'Investment Limits'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Top 5 Sectors'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable8',  PageConfigName = 'ManagedCapTable8' WHERE SubFeature = 'Top 5 Sectors'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Top 5 Issuers'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable9',  PageConfigName = 'ManagedCapTable9' WHERE SubFeature = 'Top 5 Issuers'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Asset Ratings'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable10',  PageConfigName = 'ManagedCapTable10' WHERE SubFeature = 'Asset Ratings'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Asset Classes'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable11',  PageConfigName = 'ManagedCapTable11' WHERE SubFeature = 'Asset Classes'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Currencies'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable12',  PageConfigName = 'ManagedCapTable12' WHERE SubFeature = 'Currencies'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Geographies'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable13',  PageConfigName = 'ManagedCapTable13' WHERE SubFeature = 'Geographies'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Sectors'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable14',  PageConfigName = 'ManagedCapTable14' WHERE SubFeature = 'Sectors'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Investment Portfolio'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable15',  PageConfigName = 'ManagedCapTable15' WHERE SubFeature = 'Investment Portfolio'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Capital Activity'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable16',  PageConfigName = 'ManagedCapTable16' WHERE SubFeature = 'Capital Activity'
END
GO
IF (EXISTS (SELECT * FROM [dbo].M_SubFeature WHERE [SubFeature] = 'Income Distributions (Investor Cashflow)'))
BEGIN
    UPDATE M_SubFeature SET SubFeature = 'ManagedCapTable17',  PageConfigName = 'ManagedCapTable17' WHERE SubFeature = 'Income Distributions (Investor Cashflow)'
END
GO

IF (NOT EXISTS (SELECT  [PageID] FROM [dbo].[M_PageDetails]where Name IN('Managed Accounts')))
BEGIN
	INSERT [dbo].[M_PageDetails] 
	  ([Name], [AliasName], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedPageID], [SequenceNo], [PagePath], [IsCustom])
	VALUES 
	  (N'Managed Accounts', N'Managed Accounts', NULL, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, NULL, N'/managed-accounts', 0);
END
GO
IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts') And Name = 'Investment Page')
BEGIN
  INSERT [dbo].[M_SubPageDetails] (
    [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
    [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported])
  SELECT 'Investment Page','Investment Page',(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts'),NULL,1,0,GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
END
GO
IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts') And Name='Track Record')
BEGIN
  INSERT [dbo].[M_SubPageDetails] (
    [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
    [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported])
  SELECT 'Track Record','Track Record',(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts'),NULL,1,0,GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0, 0
END
GO
IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts') And Name='Portfolio Statistics')
BEGIN
  INSERT [dbo].[M_SubPageDetails] (
    [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
    [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported])
  SELECT 'Portfolio Statistics','Portfolio Statistics',(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts'),NULL,1,0,GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0, 0
END
GO
IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts') And Name='Investment Portfolio')
BEGIN
  INSERT [dbo].[M_SubPageDetails] (
    [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
    [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported])
  SELECT 'Investment Portfolio','Investment Portfolio',(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts'),NULL,1,0,GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0, 0
END
GO
IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts') And Name='Investor CashFlow Activity')
BEGIN
  INSERT [dbo].[M_SubPageDetails] (
    [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
    [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported])
  SELECT 'Investor CashFlow Activity','Investor CashFlow Activity',(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts'),NULL,1,0,GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0, 0
END
GO
IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts') And Name='Commentaries')
BEGIN
  INSERT [dbo].[M_SubPageDetails] (
    [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
    [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported])
  SELECT 'Commentaries','Commentaries',(SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts'),NULL,1,0,GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0, 0
END
GO

IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(SELECT SubPageID FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Investment Page' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')))
BEGIN
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'Managed Account Facts', N'Managed Account Facts', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investment Page' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)

		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'Investment Summary', N'Investment Summary', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investment Page' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable1', N'Performance', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investment Page' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable2', N'NAV Data', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investment Page' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable3', N'Income Distributions (Investment Page)', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investment Page' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
END
GO
IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(SELECT SubPageID FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Track Record' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')))
BEGIN
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable4', N'Performance Attribution (Current Year)', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Track Record' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)

		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable5', N'Track Record', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Track Record' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		
END
GO
IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(SELECT SubPageID FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')))
BEGIN
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable6', N'Portfolio Statistics', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)

		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable7', N'Investment Limits', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable8', N'Top 5 Sectors', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
			INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable9', N'Top 5 Issuers', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
			INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable10', N'Asset Ratings', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
	INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable11', N'Asset Classes', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)	
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable12', N'Currencies', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)	
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable13', N'Geographies', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)	
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable14', N'Sectors', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Portfolio Statistics' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)	
END
GO
IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(SELECT SubPageID FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Investment Portfolio' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')))
BEGIN
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable15', N'Investment Portfolio', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investment Portfolio' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
END
GO
IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(SELECT SubPageID FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Investor CashFlow Activity' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')))
BEGIN
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable16', N'Capital Activity', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investor CashFlow Activity' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'ManagedCapTable17', N'Income Distributions (Investor Cashflow)', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Investor CashFlow Activity' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
END
GO
IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(SELECT SubPageID FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Commentaries' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')))
BEGIN
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'Market Commentary', N'Market Commentary', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Commentaries' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		INSERT [dbo].[M_SubPageFields] (
			[Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
			[ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
		)
		VALUES (
			N'Managed Account Commentary', N'Managed Account Commentary', (SELECT SubPageID FROM [dbo].[M_SubPageDetails]
            WHERE Name = 'Commentaries' AND PageID = (SELECT PageID FROM M_PageDetails  WHERE Name='Managed Accounts')), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
		)
		END
GO
IF NOT EXISTS(SELECT * FROM [dbo].[PageConfiguration] WHERE [Title] = 'Automated Newsletter')
BEGIN
INSERT [dbo].[PageConfiguration] ([Title], [ImagePath], [Description], [DisplayOrder], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [IsActive]) VALUES ( N'Automated Newsletter', N'newsletter.svg', N'Create Customized Newsletter', 6, getdate(), 3, NULL, NULL, 0, 1)
END
GO
IF NOT EXISTS(SELECT * FROM [dbo].[M_KpiModules] WHERE [Name] = 'ManagedCapTable1')
BEGIN

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName])
VALUES (N'ManagedCapTable1',GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable1', N'Managed CapTable1', 1, 0, 41, 0, N'ManagedCapTable1')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable2',GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable2', N'Managed CapTable2', 1, 0, 42, 0, N'ManagedCapTable2')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable3', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable3', N'Managed CapTable3', 1, 0, 43, 0, N'ManagedCapTable3')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable4', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable4', N'Managed CapTable4', 1, 0, 44, 0, N'ManagedCapTable4')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable5', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable5', N'Managed CapTable5', 1, 0, 45, 0, N'ManagedCapTable5')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable6', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable6', N'Managed CapTable6', 1, 0, 46, 0, N'ManagedCapTable6')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable7', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable7', N'Managed CapTable7', 1, 0, 47, 0, N'ManagedCapTable7')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable8', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable8', N'Managed CapTable8', 1, 0, 48, 0, N'ManagedCapTable8')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable9', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable9', N'Managed CapTable9', 1, 0, 49, 0, N'ManagedCapTable9')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable10', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable10', N'Managed CapTable10', 1, 0, 50, 0, N'ManagedCapTable10')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable11', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable10', N'Managed CapTable10', 1, 0, 50, 0, N'ManagedCapTable10')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable12', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable10', N'Managed CapTable10', 1, 0, 50, 0, N'ManagedCapTable10')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable13', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable10', N'Managed CapTable10', 1, 0, 50, 0, N'ManagedCapTable10')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable14', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable10', N'Managed CapTable10', 1, 0, 50, 0, N'ManagedCapTable10')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable15', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable10', N'Managed CapTable10', 1, 0, 50, 0, N'ManagedCapTable10')

INSERT [dbo].[M_KpiModules] ([Name], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted], [AliasName], [TabName], [IsActive], [IsFinacials], [OrderBy], [IsBulkUpload], [PageConfigFieldName]) 
VALUES (N'ManagedCapTable16', GETDATE(), 3, NULL, NULL, 0, N'Managed CapTable10', N'Managed CapTable10', 1, 0, 50, 0, N'ManagedCapTable10')

END
GO