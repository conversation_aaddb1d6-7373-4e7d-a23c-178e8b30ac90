using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Interfaces;
using System.Text.RegularExpressions;
using System.IO;
using System.Linq;
using DataAccessLayer.UnitOfWork;
using ClosedXML.Excel;
using Exports.Helpers;
using Contract.PortfolioCompany;
using System.Reflection;
using Shared;

namespace ManagedAccounts.Handlers.Queries
{
    /// <summary>
    /// Handler for downloading managed account cap table template
    /// </summary>
    public class DownloadManagedCapTableTemplateHandler : IRequestHandler<DownloadManagedCapTableTemplateQuery, DownloadManagedCapTableTemplateResult>
    {
        private readonly IManagedAccountDetailsService _managedAccountService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DownloadManagedCapTableTemplateHandler> _logger;

        public DownloadManagedCapTableTemplateHandler(
            IManagedAccountDetailsService managedAccountService,
            IUnitOfWork unitOfWork,
            ILogger<DownloadManagedCapTableTemplateHandler> logger)
        {
            _managedAccountService = managedAccountService ?? throw new ArgumentNullException(nameof(managedAccountService));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the download managed cap table template query
        /// </summary>
        /// <param name="request">The download query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<DownloadManagedCapTableTemplateResult> Handle(DownloadManagedCapTableTemplateQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing download managed cap table template for managed account ID: {ManagedAccountId}, Module ID: {ModuleName}", 
                    request.ManagedAccountId, request.ModuleName);

                // Get managed account details
                var managedAccount = await _managedAccountService.GetByIdAsync(request.ManagedAccountId);
                if (managedAccount == null)
                {
                    _logger.LogWarning("Managed account with ID: {ManagedAccountId} not found", request.ManagedAccountId);
                    return DownloadManagedCapTableTemplateResult.Failure($"Managed account with ID {request.ManagedAccountId} not found");
                }

                // Generate file name and path using the constant template
                string fileName = "ManagedCapTable_Import";
                string finalPath = GetFinalPath(fileName);

                // Get alias name from SubpageFields repository using moduleName
                var (aliasName, moduleId) = await GetAliasNameByModuleId(request.ModuleName);

                var mappedKpiItems = KPIHelper.GetCapTableKpiList(_unitOfWork, managedAccount.UAMId, [moduleId]);

                if (mappedKpiItems == null || mappedKpiItems.Count == 0)
                {
                    _logger.LogWarning("Kpi was not mapped for the managed account {ManagedAccountName}", managedAccount.ManagedAccountName);
                    return DownloadManagedCapTableTemplateResult.Failure($"Kpi was not mapped for the {managedAccount.ManagedAccountName} managed account");
                }
                // Process the Excel file with managed account data
                await ProcessManagedCapTableExcel(finalPath, managedAccount.ManagedAccountName, aliasName, moduleId, mappedKpiItems);

                // Update file name if alias name is found
                if (!string.IsNullOrEmpty(aliasName))
                {
                    fileName = $"{SanitizeFileName(managedAccount.ManagedAccountName)}_{SanitizeFileName(aliasName)}_Import";
                }

                _logger.LogInformation("Successfully generated managed cap table template for managed account ID: {ManagedAccountId}", request.ManagedAccountId);

                return DownloadManagedCapTableTemplateResult.Success(ReplacePathDots(finalPath), SanitizeFileName(fileName));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating managed cap table template for managed account ID: {ManagedAccountId}", request.ManagedAccountId);
                return DownloadManagedCapTableTemplateResult.Failure($"Failed to generate template: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the alias name from SubpageFields repository using moduleName
        /// </summary>
        /// <param name="moduleName">The module ID</param>
        /// <returns>Alias name</returns>
        private async Task<(string,int)> GetAliasNameByModuleId(string moduleName)
        {
            try
            {
                // Get the module name from M_KpiModules
                var module = await _unitOfWork.M_KpiModulesRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.IsActive && x.Name == moduleName);
                if (module == null)
                {
                    _logger.LogWarning("Module with name {ModuleName} not found", moduleName);
                    return (string.Empty,0);
                }

                // Get the alias name from SubPageFields using the module name
                var subPageField = await _unitOfWork.SubPageFieldsRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.IsActive && x.Name == module.Name);
                return (subPageField?.AliasName ?? string.Empty, module.ModuleID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting alias name for module name: {ModuleName}", moduleName);
                return (string.Empty, 0);
            }
        }

        /// <summary>
        /// Processes the Excel file with managed account data
        /// </summary>
        /// <param name="filePath">The file path</param>
        /// <param name="managedAccountName">The managed account name</param>
        /// <param name="aliasName">The alias name for sheet renaming</param>
        private static async Task ProcessManagedCapTableExcel(string filePath, string managedAccountName, string aliasName, int moduleId, List<KpiTemplate> capTableKPIs)
        {
            await Task.Run(() =>
            {
                using var workbook = new XLWorkbook(filePath);

                if (workbook.Worksheets.Count > 0)
                {
                    var worksheet = workbook.Worksheets.First(); // Get the first worksheet

                    // Sets company name in A1 cell.
                    KpiHeaderHelper.SetHeaderValueAndStyles(worksheet, managedAccountName, aliasName);
                    // sets moduleid number for reference
                    var cell2 = worksheet.Cell(5, 2);
                    cell2.SetValue($"{Constants.ModuleIdString}-{moduleId}");
                    // fil row kpi data in the sheet.
                    KpiHeaderHelper.FillData(worksheet, capTableKPIs.Where(x => x.ModuleId == moduleId && x.KpiTypeId == 1).ToList(), 6);
                    // fill column kpi data in the sheet.
                    int columnIndex = KpiHeaderHelper.FillColumnData(worksheet, capTableKPIs.Where(x => x.ModuleId == moduleId && x.KpiTypeId == 2).ToList());
                }

                workbook.Save();
            });
        }

        /// <summary>
        /// Gets the final path for the template file
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <returns>Final file path</returns>
        private static string GetFinalPath(string fileName)
        {
            string sourceTemplate = GetTemplatePath(fileName);
            string finalPath = GetDestinationPath(fileName);

            if (File.Exists(finalPath))
                File.Delete(finalPath);

            File.Copy(sourceTemplate, finalPath, true);
            return finalPath;
        }

        /// <summary>
        /// Gets the template path
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <returns>Template path</returns>
        private static string GetTemplatePath(string fileName)
        {
            string folderNameTemplate = Path.Combine("Resources", "Templates");
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), folderNameTemplate);
            EnsureDirectoryExists(templatePath);
            return Path.Combine(templatePath, fileName + ".xlsx");
        }

        /// <summary>
        /// Gets the destination path
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <returns>Destination path</returns>
        private static string GetDestinationPath(string fileName)
        {
            string destinationFolderNameTemplate = Path.Combine("Resources", "Templates", "Temp");
            string destinationTemplatePath = Path.Combine(Directory.GetCurrentDirectory(), destinationFolderNameTemplate);
            EnsureDirectoryExists(destinationTemplatePath);
            return Path.Combine(destinationTemplatePath, fileName + ".xlsx");
        }

        /// <summary>
        /// Ensures directory exists
        /// </summary>
        /// <param name="path">The directory path</param>
        private static void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);
        }

        /// <summary>
        /// Replaces path dots for security
        /// </summary>
        /// <param name="path">The path</param>
        /// <returns>Cleaned path</returns>
        private static string ReplacePathDots(string path)
        {
            return path
                .Replace("..\\", string.Empty)
                .Replace("../", string.Empty)
                .Replace("..", string.Empty);
        }

        /// <summary>
        /// Sanitizes file name by removing invalid characters
        /// </summary>
        /// <param name="fileName">The file name to sanitize</param>
        /// <returns>Sanitized file name</returns>
        private static string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return string.Empty;

            // Remove any characters that aren't alphanumeric, space, or underscore
            string sanitized = Regex.Replace(fileName, @"[^a-zA-Z0-9\s_]", "", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));

            // Ensure the filename doesn't start with a dot or space
            sanitized = sanitized.TrimStart('.', ' ');

            return string.IsNullOrWhiteSpace(sanitized) ? "unnamed" : sanitized;
        }
    }
}
