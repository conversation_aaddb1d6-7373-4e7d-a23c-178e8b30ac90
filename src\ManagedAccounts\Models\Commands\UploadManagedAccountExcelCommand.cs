using System;
using Microsoft.AspNetCore.Http;
using MediatR;
using Contract.Utility;
using System.Collections.Generic;

namespace ManagedAccounts.Models.Commands
{
    /// <summary>
    /// Command for uploading and processing Excel files for managed account data import
    /// </summary>
    public class UploadManagedAccountExcelCommand : IRequest<UploadManagedAccountExcelResult>
    {
        /// <summary>
        /// The Excel file to upload and process
        /// </summary>
        public IFormFile File { get; set; }

        /// <summary>
        /// The module name for processing the Excel data
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// The managed account ID
        /// </summary>
        public Guid ManagedAccountId { get; set; }

        /// <summary>
        /// The current user ID
        /// </summary>
        public int UserId { get; set; }
    }

    /// <summary>
    /// Result of the Excel upload and processing operation
    /// </summary>
    public class UploadManagedAccountExcelResult
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// List of processing status messages
        /// </summary>
        public List<Status> StatusList { get; set; } = new List<Status>();

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Number of rows processed
        /// </summary>
        public int ProcessedRows { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        /// <param name="statusList">Processing status list</param>
        /// <param name="processedRows">Number of rows processed</param>
        /// <returns>Successful result</returns>
        public static UploadManagedAccountExcelResult Success(List<Status> statusList, int processedRows = 0)
        {
            return new UploadManagedAccountExcelResult
            {
                IsSuccess = true,
                StatusList = statusList,
                ProcessedRows = processedRows
            };
        }

        /// <summary>
        /// Creates a failed result
        /// </summary>
        /// <param name="errorMessage">Error message</param>
        /// <returns>Failed result</returns>
        public static UploadManagedAccountExcelResult Failure(string errorMessage)
        {
            return new UploadManagedAccountExcelResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                StatusList = new List<Status>
                {
                    new Status
                    {
                        Code = "ERROR",
                        Message = errorMessage
                    }
                }
            };
        }
    }
}
